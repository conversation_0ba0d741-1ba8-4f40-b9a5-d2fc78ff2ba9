# 1. Comprehensive Data Access Solution Guide

**Refactored from:** `.ai/guides/chinook/130-comprehensive-data-access-guide.md` on 2025-07-11

## 1.1 Table of Contents

- [1.2 Overview](#12-overview)
- [1.3 CLI Data Access](#13-cli-data-access)
- [1.4 Web Interface Access](#14-web-interface-access)
- [1.5 API Access](#15-api-access)
- [1.6 Data Export/Import Facilities](#16-data-exportimport-facilities)
- [1.7 Performance Considerations](#17-performance-considerations)
- [1.8 Security & Authentication](#18-security--authentication)
- [1.9 Best Practices](#19-best-practices)

## 1.2 Overview

This guide provides comprehensive documentation for accessing Chinook database data through multiple interfaces in a **✅ greenfield single taxonomy system** implementation using the aliziodev/laravel-taxonomy package exclusively. The solution supports three primary access methods: CLI commands, web interfaces, and API endpoints with Laravel 12 modern patterns.

**Key Features:**

- **CLI Commands**: Artisan commands for data management and bulk operations
- **Web Interface**: Filament admin panel and frontend components
- **API Access**: RESTful API with authentication and rate limiting
- **Data Export/Import**: Comprehensive facilities for data interchange
- **Single Taxonomy Integration**: Unified access to taxonomy data via aliziodev/laravel-taxonomy
- **Laravel 12 Compatibility**: Modern syntax and patterns throughout

## 1.3 CLI Data Access

### 1.3.1 Artisan Commands

```bash
# Taxonomy Management Commands (aliziodev/laravel-taxonomy)
php artisan taxonomy:create {name} {--parent=} {--description=}
php artisan taxonomy:list {--taxonomy=} {--parent=}
php artisan taxonomy:export {--format=json|csv|xml}
php artisan taxonomy:import {file} {--format=json|csv|xml}

# Term Management Commands
php artisan term:create {name} {taxonomy} {--parent=} {--description=}
php artisan term:list {--taxonomy=} {--parent=}
php artisan term:attach {model} {id} {term}
php artisan term:detach {model} {id} {term}

# Chinook Data Management Commands
php artisan chinook:data:export {model} {--format=json|csv|xml}
php artisan chinook:data:import {model} {file}
php artisan chinook:data:validate
php artisan chinook:data:cleanup

# Genre Migration Commands (Compatibility)
php artisan chinook:genre:migrate-to-taxonomy
php artisan chinook:genre:validate-migration
php artisan chinook:genre:rollback-migration

# Performance Commands
php artisan chinook:cache:warm
php artisan chinook:index:rebuild
php artisan chinook:stats:generate
```

### 1.3.2 CLI Usage Examples

```bash
# Create a new taxonomy
php artisan taxonomy:create "Genres" --description="Music genre classification"

# Create a term within a taxonomy
php artisan term:create "Progressive Rock" "Genres" --parent="Rock" --description="Progressive rock subgenre"

# Export all taxonomies with terms
php artisan taxonomy:export --format=json > taxonomies.json

# Migrate existing genre data to taxonomy system
php artisan chinook:genre:migrate-to-taxonomy

# Attach genre term to artist
php artisan term:attach "ChinookArtist" 1 "Rock"

# Validate data integrity
php artisan chinook:data:validate

# Generate performance statistics
php artisan chinook:stats:generate
```

### 1.3.3 Command Implementation Examples

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Aliziodev\LaravelTaxonomy\Models\Term;

class CreateTaxonomyCommand extends Command
{
    protected $signature = 'taxonomy:create {name} {--description=} {--slug=}';
    protected $description = 'Create a new taxonomy';

    public function handle(): int
    {
        $name = $this->argument('name');
        $description = $this->option('description');
        $slug = $this->option('slug') ?? Str::slug($name);

        $taxonomy = Taxonomy::create([
            'name' => $name,
            'slug' => $slug,
            'description' => $description,
        ]);

        $this->info("Taxonomy '{$name}' created successfully with ID: {$taxonomy->id}");
        
        return Command::SUCCESS;
    }
}

class AttachTermCommand extends Command
{
    protected $signature = 'term:attach {model} {id} {term}';
    protected $description = 'Attach a term to a model instance';

    public function handle(): int
    {
        $modelClass = "App\\Models\\{$this->argument('model')}";
        $modelId = $this->argument('id');
        $termName = $this->argument('term');

        if (!class_exists($modelClass)) {
            $this->error("Model {$modelClass} does not exist");
            return Command::FAILURE;
        }

        $model = $modelClass::find($modelId);
        if (!$model) {
            $this->error("Model instance not found");
            return Command::FAILURE;
        }

        $term = Term::where('name', $termName)->first();
        if (!$term) {
            $this->error("Term '{$termName}' not found");
            return Command::FAILURE;
        }

        $model->attachTerm($term);
        $this->info("Term '{$termName}' attached to {$modelClass} #{$modelId}");
        
        return Command::SUCCESS;
    }
}
```

## 1.4 Web Interface Access

### 1.4.1 Filament Admin Panel

**Panel URL**: `/chinook-admin`

**Key Features:**

- **Taxonomy Management**: Full CRUD operations for taxonomies and terms
- **Model Management**: Complete management of all Chinook entities
- **Data Import/Export**: Web-based data interchange tools
- **Analytics Dashboard**: Real-time statistics and insights
- **User Management**: RBAC with hierarchical permissions
- **Taxonomy Assignment**: Visual interface for term assignment

**Access Patterns:**

```php
// Taxonomy Resource Access
Route::get('/chinook-admin/taxonomies', TaxonomyResource::class);
Route::get('/chinook-admin/taxonomies/{record}/edit', TaxonomyResource\Pages\EditTaxonomy::class);

// Term Management
Route::get('/chinook-admin/terms', TermResource::class);
Route::get('/chinook-admin/terms/{record}/edit', TermResource\Pages\EditTerm::class);

// Data Export Pages
Route::get('/chinook-admin/export', ExportPage::class);
Route::post('/chinook-admin/export/generate', ExportController::class);

// Analytics Dashboard
Route::get('/chinook-admin/dashboard', DashboardPage::class);
```

### 1.4.2 Frontend Web Interface

**Public Interface Features:**

- **Music Discovery**: Browse artists, albums, tracks with taxonomy filtering
- **Search Interface**: Advanced search with taxonomy-based filters
- **User Playlists**: Create and manage personal playlists
- **Data Visualization**: Charts and graphs for music analytics
- **Taxonomy Navigation**: Hierarchical browsing by genres and categories

```php
// Frontend Routes with Taxonomy Integration
Route::get('/artists', [ArtistController::class, 'index']);
Route::get('/artists/genre/{term}', [ArtistController::class, 'byGenre']);
Route::get('/browse/taxonomy/{taxonomy}', [BrowseController::class, 'byTaxonomy']);
Route::get('/search', [SearchController::class, 'index']);
```

## 1.5 API Access

### 1.5.1 RESTful API Endpoints

**Base URL**: `/api/v1/chinook`

**Authentication**: Laravel Sanctum with role-based permissions

#### Taxonomy Endpoints

```http
GET    /api/v1/chinook/taxonomies
POST   /api/v1/chinook/taxonomies
GET    /api/v1/chinook/taxonomies/{id}
PUT    /api/v1/chinook/taxonomies/{id}
DELETE /api/v1/chinook/taxonomies/{id}

# Term Operations
GET    /api/v1/chinook/taxonomies/{id}/terms
POST   /api/v1/chinook/taxonomies/{id}/terms
GET    /api/v1/chinook/terms/{id}
PUT    /api/v1/chinook/terms/{id}
DELETE /api/v1/chinook/terms/{id}

# Hierarchical Operations
GET    /api/v1/chinook/terms/{id}/children
GET    /api/v1/chinook/terms/{id}/ancestors
GET    /api/v1/chinook/terms/{id}/descendants
```

#### Music Data Endpoints with Taxonomy

```http
# Artists
GET    /api/v1/chinook/artists
POST   /api/v1/chinook/artists
GET    /api/v1/chinook/artists/{id}
PUT    /api/v1/chinook/artists/{id}
DELETE /api/v1/chinook/artists/{id}
GET    /api/v1/chinook/artists/{id}/terms
POST   /api/v1/chinook/artists/{id}/terms
DELETE /api/v1/chinook/artists/{id}/terms/{termId}

# Albums
GET    /api/v1/chinook/albums
GET    /api/v1/chinook/albums/{id}/tracks
GET    /api/v1/chinook/albums/{id}/terms
POST   /api/v1/chinook/albums/{id}/terms

# Tracks
GET    /api/v1/chinook/tracks
GET    /api/v1/chinook/tracks/{id}/terms
POST   /api/v1/chinook/tracks/{id}/terms
```

#### Data Export/Import Endpoints

```http
# Export Operations
GET    /api/v1/chinook/export/taxonomies?format=json
GET    /api/v1/chinook/export/artists?format=csv
GET    /api/v1/chinook/export/full-dataset?format=xml

# Import Operations
POST   /api/v1/chinook/import/taxonomies
POST   /api/v1/chinook/import/artists
POST   /api/v1/chinook/import/validate
```

### 1.5.2 API Usage Examples

```javascript
// Fetch all taxonomies
const taxonomies = await fetch('/api/v1/chinook/taxonomies', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json'
    }
});

// Create new taxonomy
const newTaxonomy = await fetch('/api/v1/chinook/taxonomies', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        name: 'Electronic Dance Music',
        slug: 'electronic-dance-music',
        description: 'Electronic music for dancing'
    })
});

// Attach term to artist
const attachTerm = await fetch('/api/v1/chinook/artists/1/terms', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        term_id: 5
    })
});

// Export taxonomy data
const exportData = await fetch('/api/v1/chinook/export/taxonomies?format=json', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json'
    }
});
```

## 1.6 Data Export/Import Facilities

### 1.6.1 Supported Formats

- **JSON**: Complete data structure with relationships and taxonomy terms
- **CSV**: Tabular data for spreadsheet applications
- **XML**: Structured data for system integration

### 1.6.2 Export Capabilities with Taxonomy

```php
// Export all taxonomies with hierarchy
class TaxonomyExporter
{
    public function export(string $format, array $options = []): string
    {
        $taxonomies = Taxonomy::with(['terms'])->get();

        return match($format) {
            'json' => $this->exportJson($taxonomies, $options),
            'csv' => $this->exportCsv($taxonomies, $options),
            'xml' => $this->exportXml($taxonomies, $options),
            default => throw new InvalidArgumentException("Unsupported format: {$format}")
        };
    }

    private function exportJson(Collection $taxonomies, array $options): string
    {
        $data = $taxonomies->map(function ($taxonomy) {
            return [
                'id' => $taxonomy->id,
                'name' => $taxonomy->name,
                'slug' => $taxonomy->slug,
                'description' => $taxonomy->description,
                'terms' => $taxonomy->terms->map(function ($term) {
                    return [
                        'id' => $term->id,
                        'name' => $term->name,
                        'slug' => $term->slug,
                        'description' => $term->description,
                        'sort_order' => $term->sort_order,
                    ];
                }),
            ];
        });

        return json_encode($data, JSON_PRETTY_PRINT);
    }
}

// Export Chinook entities with taxonomy relationships
class ChinookArtistExporter
{
    public function export(string $format, array $options = []): string
    {
        $artists = ChinookArtist::with(['terms.taxonomy'])->get();

        return match($format) {
            'json' => $this->exportJson($artists, $options),
            'csv' => $this->exportCsv($artists, $options),
            default => throw new InvalidArgumentException("Unsupported format: {$format}")
        };
    }

    private function exportJson(Collection $artists, array $options): string
    {
        $data = $artists->map(function ($artist) {
            return [
                'id' => $artist->id,
                'name' => $artist->name,
                'slug' => $artist->slug,
                'biography' => $artist->biography,
                'website' => $artist->website,
                'is_active' => $artist->is_active,
                'terms' => $artist->terms->map(function ($term) {
                    return [
                        'name' => $term->name,
                        'taxonomy' => $term->taxonomy->name,
                    ];
                }),
            ];
        });

        return json_encode($data, JSON_PRETTY_PRINT);
    }
}
```

### 1.6.3 Import Capabilities with Validation

```php
// Import taxonomy data with validation
class TaxonomyImporter
{
    public function import(string $file, array $options = []): ImportResult
    {
        $data = $this->parseFile($file);
        $validator = new TaxonomyValidator();

        DB::beginTransaction();

        try {
            foreach ($data as $taxonomyData) {
                if ($options['validate'] ?? true) {
                    $validator->validate($taxonomyData);
                }

                $taxonomy = Taxonomy::create([
                    'name' => $taxonomyData['name'],
                    'slug' => $taxonomyData['slug'] ?? Str::slug($taxonomyData['name']),
                    'description' => $taxonomyData['description'] ?? null,
                ]);

                foreach ($taxonomyData['terms'] ?? [] as $termData) {
                    Term::create([
                        'taxonomy_id' => $taxonomy->id,
                        'name' => $termData['name'],
                        'slug' => $termData['slug'] ?? Str::slug($termData['name']),
                        'description' => $termData['description'] ?? null,
                        'sort_order' => $termData['sort_order'] ?? 0,
                    ]);
                }
            }

            DB::commit();
            return new ImportResult(true, 'Import completed successfully');

        } catch (Exception $e) {
            DB::rollBack();
            return new ImportResult(false, "Import failed: {$e->getMessage()}");
        }
    }
}

// Bulk import with progress tracking
class BulkImporter
{
    public function import(array $files, array $options = []): void
    {
        $progressCallback = $options['progress_callback'] ?? null;
        $totalFiles = count($files);

        foreach ($files as $index => $file) {
            $importer = $this->getImporterForFile($file);
            $result = $importer->import($file, $options);

            if ($progressCallback) {
                $progressCallback($index + 1, $totalFiles, $result);
            }
        }
    }
}
```

## 1.7 Performance Considerations

### 1.7.1 Caching Strategy with Taxonomy

```php
// Taxonomy hierarchy caching
class TaxonomyCache
{
    public function getTaxonomyTree(): Collection
    {
        return Cache::remember('taxonomy_tree', 3600, function () {
            return Taxonomy::with(['terms' => function ($query) {
                $query->orderBy('sort_order');
            }])->get();
        });
    }

    public function getTermsByTaxonomy(string $taxonomyName): Collection
    {
        return Cache::remember("terms_{$taxonomyName}", 1800, function () use ($taxonomyName) {
            return Term::whereHas('taxonomy', function ($query) use ($taxonomyName) {
                $query->where('name', $taxonomyName);
            })->orderBy('sort_order')->get();
        });
    }
}

// API response caching
class ApiResponseCache
{
    public function getCachedResponse(string $key, callable $callback): mixed
    {
        return Cache::tags(['api', 'chinook'])->remember($key, 1800, $callback);
    }

    public function invalidateCache(array $tags = []): void
    {
        Cache::tags(array_merge(['api', 'chinook'], $tags))->flush();
    }
}
```

### 1.7.2 Query Optimization

```php
// Efficient taxonomy queries with Laravel 12 patterns
class OptimizedQueries
{
    public function getArtistsWithGenres(): Collection
    {
        return ChinookArtist::select(['id', 'name', 'slug'])
            ->with(['terms' => function ($query) {
                $query->select(['id', 'name', 'slug'])
                      ->whereHas('taxonomy', function ($q) {
                          $q->where('name', 'Genres');
                      });
            }])
            ->get();
    }

    public function getPopularGenres(): Collection
    {
        return Term::select(['id', 'name', 'slug'])
            ->whereHas('taxonomy', function ($query) {
                $query->where('name', 'Genres');
            })
            ->withCount('termables')
            ->orderBy('termables_count', 'desc')
            ->limit(10)
            ->get();
    }
}

// Paginated API responses with taxonomy
class PaginatedApiResponse
{
    public function getArtists(Request $request): JsonResponse
    {
        $artists = ChinookArtist::with(['terms.taxonomy'])
            ->when($request->genre, function ($query, $genre) {
                $query->whereHasTerm($genre, 'Genres');
            })
            ->paginate(50);

        return response()->json([
            'data' => ChinookArtistResource::collection($artists->items()),
            'meta' => [
                'current_page' => $artists->currentPage(),
                'total' => $artists->total(),
                'per_page' => $artists->perPage(),
            ]
        ]);
    }
}
```

## 1.8 Security & Authentication

### 1.8.1 API Authentication with Taxonomy Scope

```php
// Sanctum token authentication with taxonomy abilities
Route::middleware(['auth:sanctum', 'ability:chinook:read'])
    ->get('/api/v1/chinook/taxonomies', [TaxonomyController::class, 'index']);

Route::middleware(['auth:sanctum', 'ability:chinook:write'])
    ->post('/api/v1/chinook/taxonomies', [TaxonomyController::class, 'store']);

// Role-based access control with taxonomy permissions
Route::middleware(['auth:sanctum', 'role:admin'])
    ->delete('/api/v1/chinook/taxonomies/{taxonomy}', [TaxonomyController::class, 'destroy']);

// Taxonomy-specific permissions
Route::middleware(['auth:sanctum', 'permission:manage-genres'])
    ->group(function () {
        Route::post('/api/v1/chinook/artists/{artist}/terms', [ArtistTermController::class, 'attach']);
        Route::delete('/api/v1/chinook/artists/{artist}/terms/{term}', [ArtistTermController::class, 'detach']);
    });
```

### 1.8.2 Data Access Permissions with Taxonomy

```php
// Permission-based data filtering
class TaxonomyPolicy
{
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('view-taxonomies');
    }

    public function view(User $user, Taxonomy $taxonomy): bool
    {
        return $user->hasPermissionTo('view-taxonomies') ||
               $taxonomy->is_public;
    }

    public function create(User $user): bool
    {
        return $user->hasPermissionTo('create-taxonomies');
    }

    public function update(User $user, Taxonomy $taxonomy): bool
    {
        return $user->hasPermissionTo('update-taxonomies') ||
               ($user->hasPermissionTo('update-own-taxonomies') &&
                $taxonomy->created_by === $user->id);
    }
}

// Filtered data access based on user permissions
class SecureDataAccess
{
    public function getTaxonomies(User $user): Collection
    {
        return Taxonomy::when(!$user->can('view-all-taxonomies'), function ($query) {
            return $query->where('is_public', true);
        })->get();
    }

    public function getArtistsByGenreAccess(User $user): Collection
    {
        $accessibleGenres = $user->getTermsByTaxonomy('User Access Genres');

        return ChinookArtist::whereHas('terms', function ($query) use ($accessibleGenres) {
            $query->whereIn('id', $accessibleGenres->pluck('id'));
        })->get();
    }
}
```

## 1.9 Best Practices

### 1.9.1 CLI Commands

- **Use transactions for bulk operations** to ensure data consistency
- **Implement progress bars** for long-running commands with taxonomy operations
- **Provide verbose output options** for debugging taxonomy relationships
- **Include validation and error handling** for taxonomy data integrity

### 1.9.2 Web Interface

- **Implement proper pagination** for large taxonomy datasets
- **Use lazy loading** for related taxonomy data
- **Provide export/import progress indicators** for taxonomy operations
- **Include data validation feedback** for taxonomy assignments

### 1.9.3 API Design

- **Follow RESTful conventions** for taxonomy endpoints
- **Implement proper HTTP status codes** for taxonomy operations
- **Use consistent error response formats** across all endpoints
- **Include rate limiting and throttling** for taxonomy-heavy operations
- **Provide comprehensive API documentation** with taxonomy examples

### 1.9.4 Data Management

- **Always validate imported taxonomy data** before processing
- **Use database transactions** for consistency in taxonomy operations
- **Implement proper error logging** for taxonomy-related failures
- **Provide rollback capabilities** for taxonomy imports
- **Monitor performance metrics** for taxonomy query optimization

### 1.9.5 Laravel 12 Modern Patterns

```php
// Use casts() method instead of $casts property
protected function casts(): array
{
    return [
        'is_active' => 'boolean',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}

// Leverage enum for type safety
enum TaxonomyType: string
{
    case GENRE = 'genre';
    case ALBUM_TYPE = 'album_type';
    case MOOD = 'mood';
    case INSTRUMENT = 'instrument';
}

// Use modern validation patterns
class TaxonomyRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'unique:taxonomies'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:taxonomies'],
            'description' => ['nullable', 'string'],
            'type' => ['required', Rule::enum(TaxonomyType::class)],
        ];
    }
}
```

---

**Next**: [README Documentation](README.md) | **Previous**: [Laravel Query Builder Guide](120-laravel-query-builder-guide.md)

---

*This guide demonstrates comprehensive data access patterns for the Chinook system using Laravel 12, multiple interfaces, and the aliziodev/laravel-taxonomy package for unified taxonomy management.*
